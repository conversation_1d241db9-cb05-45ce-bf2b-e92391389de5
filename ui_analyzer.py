import json
from typing import Dict, List, Any
import chromadb
from sentence_transformers import SentenceTransformer
import google.generativeai as genai
from config import Config

class UIElementProcessor:
    """Handles processing and storage of UI elements in vector database"""

    def __init__(self):
        self.config = Config()
        # Set Hugging Face token for model downloads
        import os
        os.environ["HUGGINGFACE_HUB_TOKEN"] = self.config.HUGGINGFACE_TOKEN

        try:
            self.embeddings = SentenceTransformer(
                self.config.EMBEDDING_MODEL,
                token=self.config.HUGGINGFACE_TOKEN
            )
        except:
            # Fallback to a simpler approach without embeddings
            print("Warning: Could not load SentenceTransformer. Using simple text matching.")
            self.embeddings = None

        self.chroma_client = chromadb.PersistentClient(path=self.config.CHROMA_DB_PATH)
        self.collection = None
        self._initialize_collection()

    def _initialize_collection(self):
        """Initialize or get existing ChromaDB collection"""
        try:
            self.collection = self.chroma_client.get_collection(name=self.config.COLLECTION_NAME)
            print(f"Loaded existing collection: {self.config.COLLECTION_NAME}")
        except:
            self.collection = self.chroma_client.create_collection(
                name=self.config.COLLECTION_NAME,
                metadata={"description": "UI elements with coordinates and DOM data"}
            )
            print(f"Created new collection: {self.config.COLLECTION_NAME}")

    def process_and_store_data(self, coordinates_path: str, element_info_path: str):
        """Process and store UI element data in vector database"""
        # Load coordinates data
        with open(coordinates_path, 'r') as f:
            coordinates_data = json.load(f)

        # Load element info data
        with open(element_info_path, 'r') as f:
            element_info_data = json.load(f)

        # Clear existing data
        try:
            self.collection.delete()
        except:
            pass

        documents = []
        metadatas = []
        ids = []

        for i, coord_item in enumerate(coordinates_data):
            element_key = f"element_{i+1}"

            if element_key in element_info_data:
                element_info = element_info_data[element_key]

                # Create document text for embedding
                doc_text = self._create_document_text(coord_item, element_info)

                # Create metadata
                metadata = {
                    "element_id": element_key,
                    "label": coord_item["label"],
                    "x": coord_item["coordinates"]["x"],
                    "y": coord_item["coordinates"]["y"],
                    "width": coord_item["coordinates"]["width"],
                    "height": coord_item["coordinates"]["height"],
                    "tag": element_info.get("tag", ""),
                    "text": element_info.get("text", ""),
                    "css_selector": element_info.get("cssSelector", ""),
                    "xpath": element_info.get("xpath", "")
                }

                documents.append(doc_text)
                metadatas.append(metadata)
                ids.append(element_key)

        # Store in ChromaDB
        if documents:
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            print(f"Stored {len(documents)} UI elements in vector database")

    def _create_document_text(self, coord_item: Dict, element_info: Dict) -> str:
        """Create searchable text from UI element data"""
        text_parts = [
            f"Label: {coord_item['label']}",
            f"Tag: {element_info.get('tag', '')}",
            f"Text content: {element_info.get('text', '')}",
            f"CSS classes: {' '.join(element_info.get('classes', []))}",
            f"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}",
            f"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}",
        ]

        # Add attributes if available
        if 'attributes' in element_info:
            attrs = element_info['attributes']
            if attrs:
                attr_text = ", ".join([f"{k}={v}" for k, v in attrs.items() if v])
                text_parts.append(f"Attributes: {attr_text}")

        return " | ".join(text_parts)

    def search_relevant_elements(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """Search for relevant UI elements based on query"""
        if top_k is None:
            top_k = self.config.TOP_K_RESULTS

        if self.embeddings is not None:
            # Use vector search if embeddings are available
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k
            )

            relevant_elements = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i]
                    relevant_elements.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": results['distances'][0][i] if 'distances' in results else None
                    })
        else:
            # Fallback to simple text matching
            all_items = self.collection.get()
            relevant_elements = []

            query_lower = query.lower()
            for i, doc in enumerate(all_items['documents']):
                metadata = all_items['metadatas'][i]
                # Simple keyword matching
                if any(keyword in doc.lower() for keyword in query_lower.split()):
                    relevant_elements.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": 0.5  # Default similarity score
                    })

            # Limit results
            relevant_elements = relevant_elements[:top_k]

        return relevant_elements

class UIAnalyzer:
    """Simple UI analyzer using Gemini AI"""

    def __init__(self):
        self.config = Config()
        self.processor = UIElementProcessor()

        # Configure Gemini
        genai.configure(api_key=self.config.GOOGLE_API_KEY)
        self.model = genai.GenerativeModel(self.config.MODEL_NAME)

    def analyze_query(self, query: str, screenshot_path: str = None) -> str:
        """Analyze a user query about the UI"""
        if screenshot_path is None:
            screenshot_path = self.config.DEFAULT_SCREENSHOT_PATH

        # Search for relevant elements
        relevant_elements = self.processor.search_relevant_elements(query)

        # Generate context
        context = self._generate_context(relevant_elements)

        # Generate response using Gemini
        prompt = f"""
You are an expert UI/UX analyst. Based on the user's question about a web page and the relevant UI elements found, provide a detailed and helpful response.

User Question: {query}

Screenshot Reference: The user has provided a screenshot at {screenshot_path}

Relevant UI Elements Found:
{context}

Please provide a comprehensive answer that:
1. Directly addresses the user's question
2. References the specific UI elements and their locations
3. Explains the functionality or purpose of the elements
4. Provides coordinate information when relevant
5. Uses clear, user-friendly language

Response:
"""

        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Error generating response: {str(e)}"

    def _generate_context(self, relevant_elements: List[Dict[str, Any]]) -> str:
        """Generate context from relevant elements"""
        context_parts = []

        for element in relevant_elements:
            metadata = element["metadata"]
            context_parts.append(
                f"Element: {metadata['label']}\n"
                f"- Tag: {metadata['tag']}\n"
                f"- Text: {metadata['text']}\n"
                f"- Position: ({metadata['x']}, {metadata['y']})\n"
                f"- Size: {metadata['width']}x{metadata['height']}\n"
                f"- CSS Selector: {metadata['css_selector']}\n"
                f"- XPath: {metadata['xpath']}\n"
            )

        return "\n".join(context_parts)

    def initialize_data(self, coordinates_path: str = None, element_info_path: str = None):
        """Initialize the system with UI data"""
        if coordinates_path is None:
            coordinates_path = self.config.DEFAULT_COORDINATES_PATH
        if element_info_path is None:
            element_info_path = self.config.DEFAULT_ELEMENT_INFO_PATH

        self.processor.process_and_store_data(coordinates_path, element_info_path)
        print("UI data initialized successfully!")

def main():
    """Main function for testing"""
    # Initialize the analyzer
    analyzer = UIAnalyzer()

    # Initialize with default data
    analyzer.initialize_data()

    # Example queries
    test_queries = [
        "Where is the main heading shown on the page?",
        "What is the video element on the page?",
        "Tell me about the figure element and its location",
        "What elements are in the header area?",
        "Where can I find the brand logo?"
    ]

    print("\n" + "="*50)
    print("UI ANALYZER - TESTING")
    print("="*50)

    for query in test_queries:
        print(f"\nQuery: {query}")
        print("-" * 40)
        response = analyzer.analyze_query(query)
        print(f"Response: {response}")
        print("\n")

if __name__ == "__main__":
    main()
