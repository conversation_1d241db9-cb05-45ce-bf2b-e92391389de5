{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# 🔍 UI Element Analyzer\n", "## LangChain + Vector Database + Gemini AI for UI Analysis\n", "\n", "This notebook analyzes UI elements from screenshots using:\n", "- **Vector Database**: ChromaDB for semantic search\n", "- **Embeddings**: SentenceTransformer for UI element understanding\n", "- **AI Model**: Google Gemini for intelligent responses\n", "- **Multi-modal Input**: Screenshots, coordinates, and DOM data\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 📦 Step 1: Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install"}, "outputs": [], "source": ["# Install required packages\n", "!pip install -q chromadb sentence-transformers google-generativeai python-dotenv\n", "!pip install -q ipywidgets\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "config"}, "source": ["## 🔑 Step 2: Configuration & API Keys"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config_cell"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "import getpass\n", "\n", "# Configuration class\n", "class Config:\n", "    # API Keys - You can set these in Colab secrets or enter manually\n", "    try:\n", "        GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')\n", "        HUGGINGFACE_TOKEN = userdata.get('HUGGINGFACE_TOKEN')\n", "        print(\"✅ API keys loaded from Colab secrets\")\n", "    except:\n", "        print(\"⚠️ API keys not found in secrets. Please enter them manually:\")\n", "        GOOGLE_API_KEY = getpass.getpass(\"Enter your Google API Key: \")\n", "        HUGGINGFACE_TOKEN = getpass.getpass(\"Enter your Hugging Face Token: \")\n", "    \n", "    # Database configuration\n", "    CHROMA_DB_PATH = \"./chroma_db\"\n", "    COLLECTION_NAME = \"ui_elements\"\n", "    \n", "    # Model configuration\n", "    MODEL_NAME = \"gemini-pro\"\n", "    TEMPERATURE = 0.7\n", "    MAX_TOKENS = 1000\n", "    \n", "    # Embedding configuration\n", "    EMBEDDING_MODEL = \"all-MiniLM-L6-v2\"\n", "    TOP_K_RESULTS = 3\n", "\n", "config = Config()\n", "print(\"✅ Configuration loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "sample_data"}, "source": ["## 📁 Step 3: Create Sample Data\n", "\n", "Let's create sample UI data for demonstration:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_data"}, "outputs": [], "source": ["import json\n", "\n", "# Sample coordinates data\n", "coordinates_data = [\n", "    {\n", "        \"coordinates\": {\"x\": 949, \"y\": 385, \"width\": 626, \"height\": 330},\n", "        \"label\": \"Video\"\n", "    },\n", "    {\n", "        \"coordinates\": {\"x\": 323, \"y\": 451, \"width\": 602, \"height\": 128},\n", "        \"label\": \"Main Heading\"\n", "    },\n", "    {\n", "        \"coordinates\": {\"x\": 725, \"y\": 2666, \"width\": 447, \"height\": 90},\n", "        \"label\": \"Figure\"\n", "    }\n", "]\n", "\n", "# Sample element info data\n", "element_info_data = {\n", "    \"element_1\": {\n", "        \"attributes\": {\n", "            \"class\": \"Video_video__KYz0l Video_videoAspectRatio__qVTeE HomepageHero_image__5I40D\",\n", "            \"src\": \"/front-static/nosey/hero/noseyHeroV2.mp4\",\n", "            \"autoplay\": \"\",\n", "            \"muted\": \"\"\n", "        },\n", "        \"classes\": [\"Video_video__KYz0l\", \"Video_videoAspectRatio__qVTeE\", \"HomepageHero_image__5I40D\"],\n", "        \"cssSelector\": \"video.Video_video__KYz0l.Video_videoAspectRatio__qVTeE.HomepageHero_image__5I40D\",\n", "        \"tag\": \"video\",\n", "        \"text\": \"\",\n", "        \"xpath\": \"//*[@id=\\\"__next\\\"]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/div[2]/video[1]\"\n", "    },\n", "    \"element_2\": {\n", "        \"attributes\": {\n", "            \"class\": \"HomepageHero_heading__Nj93Y\"\n", "        },\n", "        \"classes\": [\"HomepageHero_heading__Nj93Y\"],\n", "        \"cssSelector\": \"h1.HomepageHero_heading__Nj93Y\",\n", "        \"tag\": \"h1\",\n", "        \"text\": \"The AI workspace that works for you.\",\n", "        \"xpath\": \"//*[@id=\\\"__next\\\"]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/header[1]/h1[1]\"\n", "    },\n", "    \"element_3\": {\n", "        \"attributes\": {\n", "            \"class\": \"spring_forbesQuote__qnv9M\"\n", "        },\n", "        \"classes\": [\"spring_forbesQuote__qnv9M\"],\n", "        \"cssSelector\": \"figure.spring_forbesQuote__qnv9M\",\n", "        \"tag\": \"figure\",\n", "        \"text\": \"Your AI everything app.\",\n", "        \"xpath\": \"//*[@id=\\\":RbmH4:\\\"]/div[2]/figure[1]\"\n", "    }\n", "}\n", "\n", "# Save sample data\n", "with open('coordinates.json', 'w') as f:\n", "    json.dump(coordinates_data, f, indent=2)\n", "\n", "with open('element_info.json', 'w') as f:\n", "    json.dump(element_info_data, f, indent=2)\n", "\n", "print(\"✅ Sample data created:\")\n", "print(f\"   📍 Coordinates: {len(coordinates_data)} UI elements\")\n", "print(f\"   🏷️ Element info: {len(element_info_data)} DOM elements\")"]}, {"cell_type": "markdown", "metadata": {"id": "processor"}, "source": ["## 🧠 Step 4: UI Element Processor\n", "\n", "This class handles vector database operations and semantic search:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "processor_class"}, "outputs": [], "source": ["import chromadb\n", "from sentence_transformers import SentenceTransformer\n", "from typing import Dict, List, Any\n", "\n", "class UIElementProcessor:\n", "    \"\"\"Handles processing and storage of UI elements in vector database\"\"\"\n", "    \n", "    def __init__(self, config):\n", "        self.config = config\n", "        \n", "        # Set Hugging Face token\n", "        os.environ[\"HUGGINGFACE_HUB_TOKEN\"] = config.HUGGINGFACE_TOKEN\n", "        \n", "        # Initialize embeddings\n", "        try:\n", "            print(\"🔄 Loading SentenceTransformer model...\")\n", "            self.embeddings = SentenceTransformer(\n", "                config.EMBEDDING_MODEL,\n", "                token=config.HUGGINGFACE_TOKEN\n", "            )\n", "            print(\"✅ SentenceTransformer loaded successfully!\")\n", "        except Exception as e:\n", "            print(f\"❌ Could not load SentenceTransformer: {e}\")\n", "            self.embeddings = None\n", "        \n", "        # Initialize ChromaDB\n", "        self.chroma_client = chromadb.PersistentClient(path=config.CHROMA_DB_PATH)\n", "        self.collection = None\n", "        self._initialize_collection()\n", "    \n", "    def _initialize_collection(self):\n", "        \"\"\"Initialize or get existing ChromaDB collection\"\"\"\n", "        try:\n", "            self.collection = self.chroma_client.get_collection(name=self.config.COLLECTION_NAME)\n", "            print(f\"✅ Loaded existing collection: {self.config.COLLECTION_NAME}\")\n", "        except:\n", "            self.collection = self.chroma_client.create_collection(\n", "                name=self.config.COLLECTION_NAME,\n", "                metadata={\"description\": \"UI elements with coordinates and DOM data\"}\n", "            )\n", "            print(f\"✅ Created new collection: {self.config.COLLECTION_NAME}\")\n", "    \n", "    def process_and_store_data(self, coordinates_data: List[Dict], element_info_data: Dict):\n", "        \"\"\"Process and store UI element data in vector database\"\"\"\n", "        \n", "        # Clear existing data\n", "        try:\n", "            self.collection.delete()\n", "        except:\n", "            pass\n", "        \n", "        documents = []\n", "        metadatas = []\n", "        ids = []\n", "        \n", "        for i, coord_item in enumerate(coordinates_data):\n", "            element_key = f\"element_{i+1}\"\n", "            \n", "            if element_key in element_info_data:\n", "                element_info = element_info_data[element_key]\n", "                \n", "                # Create document text for embedding\n", "                doc_text = self._create_document_text(coord_item, element_info)\n", "                \n", "                # Create metadata\n", "                metadata = {\n", "                    \"element_id\": element_key,\n", "                    \"label\": coord_item[\"label\"],\n", "                    \"x\": coord_item[\"coordinates\"][\"x\"],\n", "                    \"y\": coord_item[\"coordinates\"][\"y\"],\n", "                    \"width\": coord_item[\"coordinates\"][\"width\"],\n", "                    \"height\": coord_item[\"coordinates\"][\"height\"],\n", "                    \"tag\": element_info.get(\"tag\", \"\"),\n", "                    \"text\": element_info.get(\"text\", \"\"),\n", "                    \"css_selector\": element_info.get(\"cssSelector\", \"\"),\n", "                    \"xpath\": element_info.get(\"xpath\", \"\")\n", "                }\n", "                \n", "                documents.append(doc_text)\n", "                metadatas.append(metadata)\n", "                ids.append(element_key)\n", "        \n", "        # Store in ChromaDB\n", "        if documents:\n", "            self.collection.add(\n", "                documents=documents,\n", "                metadatas=metadatas,\n", "                ids=ids\n", "            )\n", "            print(f\"✅ Stored {len(documents)} UI elements in vector database\")\n", "    \n", "    def _create_document_text(self, coord_item: Dict, element_info: Dict) -> str:\n", "        \"\"\"Create searchable text from UI element data\"\"\"\n", "        text_parts = [\n", "            f\"Label: {coord_item['label']}\",\n", "            f\"Tag: {element_info.get('tag', '')}\",\n", "            f\"Text content: {element_info.get('text', '')}\",\n", "            f\"CSS classes: {' '.join(element_info.get('classes', []))}\",\n", "            f\"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}\",\n", "            f\"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}\",\n", "        ]\n", "        \n", "        # Add attributes if available\n", "        if 'attributes' in element_info:\n", "            attrs = element_info['attributes']\n", "            if attrs:\n", "                attr_text = \", \".join([f\"{k}={v}\" for k, v in attrs.items() if v])\n", "                text_parts.append(f\"Attributes: {attr_text}\")\n", "        \n", "        return \" | \".join(text_parts)\n", "    \n", "    def search_relevant_elements(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:\n", "        \"\"\"Search for relevant UI elements based on query\"\"\"\n", "        if top_k is None:\n", "            top_k = self.config.TOP_K_RESULTS\n", "        \n", "        if self.embeddings is not None:\n", "            # Use vector search if embeddings are available\n", "            results = self.collection.query(\n", "                query_texts=[query],\n", "                n_results=top_k\n", "            )\n", "            \n", "            relevant_elements = []\n", "            if results['documents'] and results['documents'][0]:\n", "                for i, doc in enumerate(results['documents'][0]):\n", "                    metadata = results['metadatas'][0][i]\n", "                    relevant_elements.append({\n", "                        \"document\": doc,\n", "                        \"metadata\": metadata,\n", "                        \"distance\": results['distances'][0][i] if 'distances' in results else None\n", "                    })\n", "        else:\n", "            # Fallback to simple text matching\n", "            all_items = self.collection.get()\n", "            relevant_elements = []\n", "            \n", "            query_lower = query.lower()\n", "            for i, doc in enumerate(all_items['documents']):\n", "                metadata = all_items['metadatas'][i]\n", "                # Simple keyword matching\n", "                if any(keyword in doc.lower() for keyword in query_lower.split()):\n", "                    relevant_elements.append({\n", "                        \"document\": doc,\n", "                        \"metadata\": metadata,\n", "                        \"distance\": 0.5  # Default similarity score\n", "                    })\n", "            \n", "            # Limit results\n", "            relevant_elements = relevant_elements[:top_k]\n", "        \n", "        return relevant_elements\n", "\n", "print(\"✅ UIElementProcessor class defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "analyzer"}, "source": ["## 🤖 Step 5: UI Analyzer with Gemini AI"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "analyzer_class"}, "outputs": [], "source": ["import google.generativeai as genai\n", "\n", "class UIAnalyzer:\n", "    \"\"\"Main UI analyzer using Gemini AI\"\"\"\n", "    \n", "    def __init__(self, config):\n", "        self.config = config\n", "        self.processor = UIElementProcessor(config)\n", "        \n", "        # Configure Gemini\n", "        genai.configure(api_key=config.GOOGLE_API_KEY)\n", "        self.model = genai.GenerativeModel(config.MODEL_NAME)\n", "        print(\"✅ UIAnalyzer initialized with Gemini AI!\")\n", "    \n", "    def initialize_data(self, coordinates_data: List[Dict], element_info_data: Dict):\n", "        \"\"\"Initialize the system with UI data\"\"\"\n", "        self.processor.process_and_store_data(coordinates_data, element_info_data)\n", "        print(\"✅ UI data initialized successfully!\")\n", "    \n", "    def analyze_query(self, query: str, screenshot_path: str = \"screenshot.png\") -> str:\n", "        \"\"\"Analyze a user query about the UI\"\"\"\n", "        \n", "        # Search for relevant elements\n", "        relevant_elements = self.processor.search_relevant_elements(query)\n", "        \n", "        # Generate context\n", "        context = self._generate_context(relevant_elements)\n", "        \n", "        # Generate response using Gemini\n", "        prompt = f\"\"\"\n", "You are an expert UI/UX analyst. Based on the user's question about a web page and the relevant UI elements found, provide a detailed and helpful response.\n", "\n", "User Question: {query}\n", "\n", "Screenshot Reference: The user has provided a screenshot at {screenshot_path}\n", "\n", "Relevant UI Elements Found:\n", "{context}\n", "\n", "Please provide a comprehensive answer that:\n", "1. Directly addresses the user's question\n", "2. References the specific UI elements and their locations\n", "3. Explains the functionality or purpose of the elements\n", "4. Provides coordinate information when relevant\n", "5. Uses clear, user-friendly language\n", "\n", "Response:\n", "\"\"\"\n", "        \n", "        try:\n", "            response = self.model.generate_content(prompt)\n", "            return response.text\n", "        except Exception as e:\n", "            return f\"Error generating response: {str(e)}\"\n", "    \n", "    def _generate_context(self, relevant_elements: List[Dict[str, Any]]) -> str:\n", "        \"\"\"Generate context from relevant elements\"\"\"\n", "        context_parts = []\n", "        \n", "        for element in relevant_elements:\n", "            metadata = element[\"metadata\"]\n", "            context_parts.append(\n", "                f\"Element: {metadata['label']}\\n\"\n", "                f\"- Tag: {metadata['tag']}\\n\"\n", "                f\"- Text: {metadata['text']}\\n\"\n", "                f\"- Position: ({metadata['x']}, {metadata['y']})\\n\"\n", "                f\"- Size: {metadata['width']}x{metadata['height']}\\n\"\n", "                f\"- CSS Selector: {metadata['css_selector']}\\n\"\n", "                f\"- XPath: {metadata['xpath']}\\n\"\n", "            )\n", "        \n", "        return \"\\n\".join(context_parts)\n", "\n", "print(\"✅ UIAnalyzer class defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "initialize"}, "source": ["## 🚀 Step 6: Initialize the System"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "init_system"}, "outputs": [], "source": ["# Initialize the UI Analyzer\n", "print(\"🔄 Initializing UI Element Analyzer...\")\n", "analyzer = UIAnalyzer(config)\n", "\n", "# Load sample data\n", "print(\"\\n📁 Loading sample data...\")\n", "with open('coordinates.json', 'r') as f:\n", "    coordinates_data = json.load(f)\n", "\n", "with open('element_info.json', 'r') as f:\n", "    element_info_data = json.load(f)\n", "\n", "# Initialize with data\n", "analyzer.initialize_data(coordinates_data, element_info_data)\n", "\n", "print(\"\\n🎉 System ready for queries!\")"]}, {"cell_type": "markdown", "metadata": {"id": "demo"}, "source": ["## 💬 Step 7: Interactive Demo\n", "\n", "Now you can ask questions about the UI elements!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "demo_queries"}, "outputs": [], "source": ["# Demo queries\n", "demo_queries = [\n", "    \"Where is the main heading shown on the page?\",\n", "    \"What is the video element on the page?\",\n", "    \"Tell me about the figure element and its location\",\n", "    \"What elements are in the header area?\",\n", "    \"Where can I find interactive elements?\"\n", "]\n", "\n", "print(\"🔍 DEMO: Testing UI Element Analyzer\")\n", "print(\"=\" * 50)\n", "\n", "for i, query in enumerate(demo_queries, 1):\n", "    print(f\"\\n🔍 Query {i}: {query}\")\n", "    print(\"-\" * 40)\n", "    \n", "    response = analyzer.analyze_query(query)\n", "    print(f\"🤖 Response:\\n{response}\")\n", "    print(\"-\" * 40)"]}, {"cell_type": "markdown", "metadata": {"id": "interactive"}, "source": ["## 🎯 Step 8: Interactive Query Interface\n", "\n", "Ask your own questions about the UI elements:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "interactive_query"}, "outputs": [], "source": ["import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "\n", "# Create interactive widgets\n", "query_input = widgets.Text(\n", "    value=\"\",\n", "    placeholder=\"Ask about UI elements...\",\n", "    description=\"Your Question:\",\n", "    style={'description_width': 'initial'},\n", "    layout=widgets.Layout(width='70%')\n", ")\n", "\n", "submit_button = widgets.Button(\n", "    description=\"🔍 Analyze\",\n", "    button_style='primary',\n", "    layout=widgets.Layout(width='20%')\n", ")\n", "\n", "output_area = widgets.Output()\n", "\n", "def on_submit_click(b):\n", "    with output_area:\n", "        clear_output()\n", "        if query_input.value.strip():\n", "            print(f\"🔍 Question: {query_input.value}\")\n", "            print(\"\\n🤖 Analyzing...\")\n", "            \n", "            try:\n", "                response = analyzer.analyze_query(query_input.value)\n", "                print(f\"\\n📝 Response:\\n{response}\")\n", "            except Exception as e:\n", "                print(f\"❌ Error: {str(e)}\")\n", "        else:\n", "            print(\"⚠️ Please enter a question!\")\n", "\n", "submit_button.on_click(on_submit_click)\n", "\n", "# Display interface\n", "print(\"🎯 Interactive UI Element Analyzer\")\n", "print(\"Ask questions about the UI elements below:\")\n", "display(widgets.HBox([query_input, submit_button]))\n", "display(output_area)\n", "\n", "# Example questions\n", "print(\"\\n💡 Example questions you can ask:\")\n", "for i, example in enumerate(demo_queries, 1):\n", "    print(f\"   {i}. {example}\")"]}, {"cell_type": "markdown", "metadata": {"id": "upload"}, "source": ["## 📤 Step 9: Upload Your Own Data\n", "\n", "Upload your own screenshot, coordinates, and DOM data:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["from google.colab import files\n", "import json\n", "\n", "def upload_custom_data():\n", "    print(\"📤 Upload your UI analysis files:\")\n", "    print(\"1. Screenshot image (PNG/JPG)\")\n", "    print(\"2. Coordinates JSON file\")\n", "    print(\"3. Element info JSON file\")\n", "    print()\n", "    \n", "    # Upload files\n", "    uploaded = files.upload()\n", "    \n", "    # Process uploaded files\n", "    coordinates_file = None\n", "    element_info_file = None\n", "    screenshot_file = None\n", "    \n", "    for filename in uploaded.keys():\n", "        if 'coordinate' in filename.lower() and filename.endswith('.json'):\n", "            coordinates_file = filename\n", "        elif 'element' in filename.lower() and filename.endswith('.json'):\n", "            element_info_file = filename\n", "        elif filename.lower().endswith(('.png', '.jpg', '.jpeg')):\n", "            screenshot_file = filename\n", "    \n", "    if coordinates_file and element_info_file:\n", "        try:\n", "            # Load the data\n", "            with open(coordinates_file, 'r') as f:\n", "                new_coordinates_data = json.load(f)\n", "            \n", "            with open(element_info_file, 'r') as f:\n", "                new_element_info_data = json.load(f)\n", "            \n", "            # Initialize with new data\n", "            analyzer.initialize_data(new_coordinates_data, new_element_info_data)\n", "            \n", "            print(f\"✅ Successfully loaded:\")\n", "            print(f\"   📍 {len(new_coordinates_data)} coordinate entries\")\n", "            print(f\"   🏷️ {len(new_element_info_data)} element info entries\")\n", "            if screenshot_file:\n", "                print(f\"   📷 Screenshot: {screenshot_file}\")\n", "            \n", "            print(\"\\n🎉 Ready to analyze your custom UI data!\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error processing files: {str(e)}\")\n", "    else:\n", "        print(\"⚠️ Please upload both coordinates.json and element_info.json files\")\n", "\n", "# Uncomment the line below to upload custom data\n", "# upload_custom_data()"]}, {"cell_type": "markdown", "metadata": {"id": "summary"}, "source": ["## 📊 Summary\n", "\n", "🎉 **Congratulations!** You've successfully set up the UI Element Analyzer!\n", "\n", "### ✅ What's Working:\n", "- **Vector Database**: ChromaDB with semantic search\n", "- **Embeddings**: SentenceTransformer for UI understanding\n", "- **AI Model**: Google Gemini for intelligent responses\n", "- **Multi-modal Input**: Screenshots, coordinates, and DOM data\n", "- **Interactive Interface**: Ask questions about UI elements\n", "\n", "### 🔧 Key Features:\n", "1. **Semantic Search**: Find relevant UI elements using natural language\n", "2. **Coordinate Mapping**: Precise location information for each element\n", "3. **DOM Integration**: Rich metadata from web page structure\n", "4. **AI Responses**: Context-aware answers using Gemini AI\n", "5. **Custom Data**: Upload your own UI analysis files\n", "\n", "### 🚀 Next Steps:\n", "- Upload your own screenshot and data files\n", "- Ask specific questions about your UI elements\n", "- Integrate this into your web development workflow\n", "- Extend with additional UI element types\n", "\n", "---\n", "\n", "**Happy UI Analyzing!** 🔍✨"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}