# 🚀 Google Colab Instructions for UI Element Analyzer

## 📋 Quick Start Guide

### 1. **Open in Google Colab**
- Upload the `UI_Element_Analyzer_Colab.ipynb` file to Google Colab
- Or use this link: [Open in Colab](https://colab.research.google.com/)

### 2. **Set Up API Keys**

#### Option A: Using Colab Secrets (Recommended)
1. Click the 🔑 key icon in the left sidebar
2. Add these secrets:
   - `GOOGLE_API_KEY`: Your Google AI Studio API key
   - `HUGGINGFACE_TOKEN`: `*************************************`

#### Option B: Manual Entry
- The notebook will prompt you to enter API keys if not found in secrets

### 3. **Get Google API Key**
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy and add it to Colab secrets or enter when prompted

### 4. **Run the Notebook**
- Execute cells in order (Ctrl+Enter or Shift+Enter)
- The notebook will automatically:
  - Install required packages
  - Set up the vector database
  - Load sample UI data
  - Initialize the AI analyzer

## 🎯 Features Available

### ✅ **What You Can Do:**
1. **Analyze Sample Data**: Pre-loaded Notion.com UI elements
2. **Ask Questions**: Natural language queries about UI elements
3. **Interactive Interface**: Real-time question answering
4. **Upload Custom Data**: Your own screenshots and UI data
5. **Vector Search**: Semantic similarity matching
6. **AI Responses**: Detailed answers from Gemini AI

### 📝 **Example Questions:**
- "Where is the main heading shown on the page?"
- "What is the video element on the page?"
- "Tell me about the figure element and its location"
- "What elements are in the header area?"
- "Where can I find interactive elements?"

## 📁 **Data Format**

### Coordinates JSON:
```json
[
  {
    "coordinates": {"x": 949, "y": 385, "width": 626, "height": 330},
    "label": "Video"
  }
]
```

### Element Info JSON:
```json
{
  "element_1": {
    "tag": "video",
    "text": "",
    "cssSelector": "video.Video_video__KYz0l",
    "xpath": "//*[@id='__next']/div[1]/video[1]",
    "attributes": {"class": "Video_video__KYz0l", "src": "video.mp4"}
  }
}
```

## 🔧 **Troubleshooting**

### Common Issues:
1. **API Key Errors**: Make sure your Google API key is valid and has Gemini access
2. **Package Installation**: Restart runtime if packages fail to install
3. **Memory Issues**: Use smaller datasets or restart runtime
4. **Upload Errors**: Ensure JSON files are properly formatted

### Solutions:
- **Restart Runtime**: Runtime → Restart runtime
- **Clear Output**: Runtime → Restart and run all
- **Check API Keys**: Verify keys are correctly set in secrets

## 📊 **Expected Output**

When working correctly, you should see:
```
✅ All packages installed successfully!
✅ API keys loaded from Colab secrets
✅ Configuration loaded successfully!
✅ SentenceTransformer loaded successfully!
✅ Created new collection: ui_elements
✅ Stored 3 UI elements in vector database
✅ UIAnalyzer initialized with Gemini AI!
🎉 System ready for queries!
```

## 🎉 **Success Indicators**

- ✅ No error messages during setup
- ✅ Vector database shows stored elements
- ✅ Interactive widgets appear
- ✅ AI responses are generated
- ✅ Custom data uploads work

## 🚀 **Advanced Usage**

### Custom Data Upload:
1. Prepare your files:
   - Screenshot image (PNG/JPG)
   - Coordinates JSON with UI element positions
   - Element info JSON with DOM data
2. Run the upload cell
3. Select and upload your files
4. System will automatically process and index your data

### Integration Tips:
- Save successful queries for reuse
- Export results for documentation
- Combine with web scraping tools
- Use for UI testing automation

## 📞 **Support**

If you encounter issues:
1. Check the troubleshooting section
2. Verify API keys are correct
3. Ensure data format matches examples
4. Restart runtime and try again

---

**Happy UI Analyzing in Colab!** 🔍✨
